package com.folder.sync.server.websocket;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.folder.sync.server.domain.model.FileSyncMessage;
import com.folder.sync.server.service.FileDataService;
import com.folder.sync.server.service.UserSessionService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.web.reactive.socket.WebSocketSession;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ReactiveWebSocketHandlerTest {

    @Mock
    private FileDataService fileDataService;

    @Mock
    private UserSessionService userSessionService;

    @Mock
    private WebSocketSession webSocketSession;

    private ReactiveWebSocketHandler handler;
    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        objectMapper = new ObjectMapper();
        handler = new ReactiveWebSocketHandler(fileDataService, userSessionService, objectMapper);
    }

    @Test
    void testGetActiveSessionCount() {
        // Initially should be 0
        int count = handler.getActiveSessionCount();
        assert count == 0;
    }

    @Test
    void testSendToUser() {
        // Mock the services
        when(userSessionService.createSession(anyString(), anyString())).thenReturn(Mono.empty());
        when(fileDataService.createUserDirectory(anyString())).thenReturn(Mono.empty());
        when(fileDataService.getExistingFileNamesForUser(anyString())).thenReturn(reactor.core.publisher.Flux.empty());

        FileSyncMessage message = FileSyncMessage.builder()
            .type("TEST")
            .message("Test message")
            .build();

        // Test sending to non-existent user
        StepVerifier.create(handler.sendToUser("nonexistent", message))
            .verifyComplete();
    }
}
