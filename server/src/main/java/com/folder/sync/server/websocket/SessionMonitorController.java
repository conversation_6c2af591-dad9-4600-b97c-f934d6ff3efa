package com.folder.sync.server.websocket;

import com.folder.sync.server.domain.model.SessionMonitorMessage;
import com.folder.sync.server.service.SessionMonitorService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.messaging.simp.annotation.SendToUser;
import org.springframework.stereotype.Controller;
import reactor.core.publisher.Mono;

@Controller  // Re-enabled for STOMP compatibility
@Slf4j
public class SessionMonitorController {

    private final SessionMonitorService sessionMonitorService;

    @Autowired
    public SessionMonitorController(SessionMonitorService sessionMonitorService) {
        this.sessionMonitorService = sessionMonitorService;
    }

    @MessageMapping("/monitor/sessions")
    @SendToUser("/queue/monitor-sessions")
    public Mono<SessionMonitorMessage> getActiveSessions() {
        log.debug("Received request for active sessions list");
        return sessionMonitorService.getCurrentSessions()
            .collectList()
            .map(sessions -> {
                SessionMonitorMessage message = new SessionMonitorMessage();
                message.setType("SESSION_LIST");
                message.setActiveSessions(sessions);
                return message;
            })
            .doOnSuccess(message -> log.debug("Sent {} active sessions", message.getActiveSessions().size()))
            .doOnError(error -> log.error("Error getting active sessions: {}", error.getMessage()));
    }
}
