package com.folder.sync.server.websocket;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.folder.sync.server.domain.model.FileSyncMessage;
import com.folder.sync.server.service.FileDataService;
import com.folder.sync.server.service.UserSessionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.socket.WebSocketHandler;
import org.springframework.web.reactive.socket.WebSocketMessage;
import org.springframework.web.reactive.socket.WebSocketSession;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.publisher.Sinks;

import java.util.concurrent.ConcurrentHashMap;
import java.util.Map;

/**
 * Reactive WebSocket Handler for file synchronization
 * Handles all WebSocket connections and message processing reactively
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class ReactiveWebSocketHandler implements WebSocketHandler {

    private final FileDataService fileDataService;
    private final UserSessionService userSessionService;
    private final ObjectMapper objectMapper;
    
    // Store active sessions and their message sinks
    private final Map<String, WebSocketSession> activeSessions = new ConcurrentHashMap<>();
    private final Map<String, Sinks.Many<String>> sessionSinks = new ConcurrentHashMap<>();
    private final Map<String, String> sessionUsernames = new ConcurrentHashMap<>();

    @Override
    public Mono<Void> handle(WebSocketSession session) {
        String sessionId = session.getId();
        log.info("New WebSocket connection established: {}", sessionId);
        
        // Store the session
        activeSessions.put(sessionId, session);
        
        // Create a sink for this session to send messages
        Sinks.Many<String> sink = Sinks.many().multicast().onBackpressureBuffer();
        sessionSinks.put(sessionId, sink);
        
        // Handle incoming messages
        Mono<Void> input = session.receive()
            .map(WebSocketMessage::getPayloadAsText)
            .flatMap(message -> handleIncomingMessage(session, message))
            .doOnError(error -> log.error("Error handling incoming message for session {}: {}", sessionId, error.getMessage()))
            .onErrorResume(error -> Mono.empty())
            .then();
        
        // Handle outgoing messages
        Mono<Void> output = session.send(
            sink.asFlux()
                .map(session::textMessage)
        );
        
        // Combine input and output, cleanup on completion
        return Mono.zip(input, output)
            .doFinally(signalType -> {
                log.info("WebSocket session {} closed with signal: {}", sessionId, signalType);
                cleanup(sessionId);
            })
            .then();
    }

    /**
     * Handle incoming WebSocket messages
     */
    private Mono<Void> handleIncomingMessage(WebSocketSession session, String messageText) {
        String sessionId = session.getId();
        
        return Mono.fromCallable(() -> {
            try {
                return objectMapper.readValue(messageText, FileSyncMessage.class);
            } catch (JsonProcessingException e) {
                log.error("Failed to parse message from session {}: {}", sessionId, e.getMessage());
                throw new RuntimeException("Invalid message format", e);
            }
        })
        .flatMap(message -> processMessage(session, message))
        .doOnError(error -> {
            log.error("Error processing message for session {}: {}", sessionId, error.getMessage());
            sendErrorMessage(sessionId, "Error processing message: " + error.getMessage());
        })
        .onErrorResume(error -> Mono.empty())
        .then();
    }

    /**
     * Process different types of messages
     */
    private Mono<Void> processMessage(WebSocketSession session, FileSyncMessage message) {
        String sessionId = session.getId();
        
        return switch (message.getType()) {
            case "CONNECT" -> handleConnect(session, message);
            case "FILE_UPLOAD" -> handleFileUpload(sessionId, message);
            case "FILE_LIST_REQUEST" -> handleFileListRequest(sessionId);
            case "PING" -> handlePing(sessionId);
            default -> {
                log.warn("Unknown message type: {} from session: {}", message.getType(), sessionId);
                sendErrorMessage(sessionId, "Unknown message type: " + message.getType());
                yield Mono.empty();
            }
        };
    }

    /**
     * Handle connection and authentication
     */
    private Mono<Void> handleConnect(WebSocketSession session, FileSyncMessage message) {
        String sessionId = session.getId();
        String rawUsername = extractUsername(session, message);

        if (rawUsername == null || rawUsername.trim().isEmpty()) {
            log.warn("No username provided for session: {}", sessionId);
            sendErrorMessage(sessionId, "Username is required for connection");
            return Mono.empty();
        }

        final String username = rawUsername.trim();
        sessionUsernames.put(sessionId, username);

        log.info("User {} connected with session: {}", username, sessionId);

        return userSessionService.createSession(sessionId, username)
            .then(fileDataService.createUserDirectory(username))
            .then(getExistingFileList(username))
            .flatMap(fileList -> {
                FileSyncMessage response = FileSyncMessage.builder()
                    .type("FILE_LIST")
                    .fileList(fileList)
                    .message("Connected successfully. Here are your existing files.")
                    .build();
                return sendMessage(sessionId, response);
            })
            .doOnSuccess(unused -> log.info("User {} connected successfully", username))
            .doOnError(error -> {
                log.error("Error connecting user {}: {}", username, error.getMessage());
                sendErrorMessage(sessionId, "Connection failed: " + error.getMessage());
            })
            .onErrorResume(error -> Mono.empty())
            .then();
    }

    /**
     * Handle file upload
     */
    private Mono<Void> handleFileUpload(String sessionId, FileSyncMessage message) {
        String username = sessionUsernames.get(sessionId);
        if (username == null) {
            sendErrorMessage(sessionId, "Not authenticated");
            return Mono.empty();
        }
        
        return userSessionService.updateSessionActivity(sessionId)
            .then(fileDataService.processFileData(
                message.getFilePath(),
                message.getContent(),
                message.getFileSize(),
                message.getLastModified(),
                username
            ))
            .flatMap(savedFile -> {
                FileSyncMessage response = FileSyncMessage.builder()
                    .type("FILE_UPLOAD_SUCCESS")
                    .filePath(savedFile.getOriginalFilePath())
                    .message("File uploaded successfully")
                    .build();
                return sendMessage(sessionId, response);
            })
            .doOnError(error -> {
                log.error("Error uploading file for user {}: {}", username, error.getMessage());
                sendErrorMessage(sessionId, "Failed to upload file: " + error.getMessage());
            })
            .onErrorResume(error -> Mono.empty())
            .then();
    }

    /**
     * Handle file list request
     */
    private Mono<Void> handleFileListRequest(String sessionId) {
        String username = sessionUsernames.get(sessionId);
        if (username == null) {
            sendErrorMessage(sessionId, "Not authenticated");
            return Mono.empty();
        }
        
        return userSessionService.updateSessionActivity(sessionId)
            .then(getExistingFileList(username))
            .flatMap(fileList -> {
                FileSyncMessage response = FileSyncMessage.builder()
                    .type("FILE_LIST")
                    .fileList(fileList)
                    .message("File list retrieved successfully")
                    .build();
                return sendMessage(sessionId, response);
            })
            .doOnError(error -> {
                log.error("Error getting file list for user {}: {}", username, error.getMessage());
                sendErrorMessage(sessionId, "Failed to get file list: " + error.getMessage());
            })
            .onErrorResume(error -> Mono.empty())
            .then();
    }

    /**
     * Handle ping message
     */
    private Mono<Void> handlePing(String sessionId) {
        String username = sessionUsernames.get(sessionId);
        if (username == null) {
            sendErrorMessage(sessionId, "Not authenticated");
            return Mono.empty();
        }
        
        return userSessionService.updateSessionActivity(sessionId)
            .then(Mono.fromRunnable(() -> {
                FileSyncMessage response = FileSyncMessage.builder()
                    .type("PONG")
                    .message("Server is alive")
                    .build();
                sendMessage(sessionId, response).subscribe();
            }));
    }

    /**
     * Get existing file list for user
     */
    private Mono<java.util.List<String>> getExistingFileList(String username) {
        return fileDataService.getExistingFileNamesForUser(username)
            .collectList();
    }

    /**
     * Send a message to a specific session
     */
    private Mono<Void> sendMessage(String sessionId, FileSyncMessage message) {
        return Mono.fromCallable(() -> {
            try {
                return objectMapper.writeValueAsString(message);
            } catch (JsonProcessingException e) {
                log.error("Failed to serialize message for session {}: {}", sessionId, e.getMessage());
                throw new RuntimeException("Failed to serialize message", e);
            }
        })
        .flatMap(messageJson -> {
            Sinks.Many<String> sink = sessionSinks.get(sessionId);
            if (sink != null) {
                Sinks.EmitResult result = sink.tryEmitNext(messageJson);
                if (result.isFailure()) {
                    log.warn("Failed to emit message to session {}: {}", sessionId, result);
                }
            }
            return Mono.empty();
        });
    }

    /**
     * Send error message to session
     */
    private void sendErrorMessage(String sessionId, String errorMessage) {
        FileSyncMessage errorResponse = FileSyncMessage.builder()
            .type("ERROR")
            .message(errorMessage)
            .build();
        sendMessage(sessionId, errorResponse).subscribe();
    }

    /**
     * Extract username from session or message
     */
    private String extractUsername(WebSocketSession session, FileSyncMessage message) {
        // Try to get username from message first
        if (message.getUsername() != null && !message.getUsername().trim().isEmpty()) {
            return message.getUsername().trim();
        }
        
        // Try to get from session headers
        return session.getHandshakeInfo().getHeaders().getFirst("user");
    }

    /**
     * Cleanup session resources
     */
    private void cleanup(String sessionId) {
        activeSessions.remove(sessionId);
        sessionUsernames.remove(sessionId);
        
        Sinks.Many<String> sink = sessionSinks.remove(sessionId);
        if (sink != null) {
            sink.tryEmitComplete();
        }
        
        log.debug("Cleaned up resources for session: {}", sessionId);
    }

    /**
     * Get active session count
     */
    public int getActiveSessionCount() {
        return activeSessions.size();
    }

    /**
     * Send message to specific user
     */
    public Mono<Void> sendToUser(String username, FileSyncMessage message) {
        return Flux.fromIterable(sessionUsernames.entrySet())
            .filter(entry -> username.equals(entry.getValue()))
            .map(Map.Entry::getKey)
            .flatMap(sessionId -> sendMessage(sessionId, message))
            .then();
    }
}
