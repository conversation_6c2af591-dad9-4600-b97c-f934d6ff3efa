package com.folder.sync.server.controller;

import com.folder.sync.server.domain.model.FileSyncMessage;
import com.folder.sync.server.service.FileDataService;
import com.folder.sync.server.websocket.ReactiveSessionManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.List;

/**
 * Reactive REST controller for file synchronization
 * Provides HTTP endpoints that work with the reactive WebSocket system
 */
@RestController
@RequestMapping("/api/reactive")
@RequiredArgsConstructor
@Slf4j
public class ReactiveFileSyncController {

    private final FileDataService fileDataService;
    private final ReactiveSessionManager sessionManager;

    /**
     * Get file count for a specific user
     */
    @GetMapping("/files/count/{username}")
    public Mono<Long> getFileCount(@PathVariable String username) {
        log.debug("Getting file count for user: {}", username);
        return fileDataService.getFileCountForUser(username);
    }

    /**
     * Get file list for a specific user
     */
    @GetMapping("/files/{username}")
    public Flux<String> getFileList(@PathVariable String username) {
        log.debug("Getting file list for user: {}", username);
        return fileDataService.getExistingFileNamesForUser(username);
    }

    /**
     * Get active session count
     */
    @GetMapping("/sessions/count")
    public Mono<Integer> getActiveSessionCount() {
        return sessionManager.getActiveSessionCount();
    }

    /**
     * Get active sessions
     */
    @GetMapping("/sessions")
    public Flux<ReactiveSessionManager.SessionInfo> getActiveSessions() {
        return sessionManager.getActiveSessions();
    }

    /**
     * Check if user is online
     */
    @GetMapping("/users/{username}/online")
    public Mono<Boolean> isUserOnline(@PathVariable String username) {
        return sessionManager.isUserOnline(username);
    }

    /**
     * Send message to specific user via WebSocket
     */
    @PostMapping("/users/{username}/message")
    public Mono<Void> sendMessageToUser(@PathVariable String username, 
                                       @RequestBody FileSyncMessage message) {
        log.info("Sending message to user: {} via WebSocket", username);
        return sessionManager.sendToUser(username, message);
    }

    /**
     * Broadcast message to all connected users
     */
    @PostMapping("/broadcast")
    public Mono<Void> broadcastMessage(@RequestBody FileSyncMessage message) {
        log.info("Broadcasting message to all users via WebSocket");
        return sessionManager.broadcast(message);
    }

    /**
     * Server-Sent Events stream for real-time session updates
     */
    @GetMapping(value = "/sessions/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<ReactiveSessionManager.SessionInfo> getSessionStream() {
        return Flux.interval(Duration.ofSeconds(5))
            .flatMap(tick -> sessionManager.getActiveSessions())
            .distinctUntilChanged();
    }

    /**
     * Server-Sent Events stream for file count updates
     */
    @GetMapping(value = "/files/count/{username}/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<Long> getFileCountStream(@PathVariable String username) {
        return Flux.interval(Duration.ofSeconds(10))
            .flatMap(tick -> fileDataService.getFileCountForUser(username))
            .distinctUntilChanged();
    }

    /**
     * Health check endpoint
     */
    @GetMapping("/health")
    public Mono<String> health() {
        return Mono.just("Reactive File Sync Server is running");
    }

    /**
     * Trigger file list update for a user (sends via WebSocket)
     */
    @PostMapping("/users/{username}/refresh-files")
    public Mono<Void> refreshFileListForUser(@PathVariable String username) {
        log.info("Refreshing file list for user: {}", username);
        
        return fileDataService.getExistingFileNamesForUser(username)
            .collectList()
            .flatMap(fileList -> {
                FileSyncMessage message = FileSyncMessage.builder()
                    .type("FILE_LIST")
                    .fileList(fileList)
                    .message("File list updated")
                    .build();
                return sessionManager.sendToUser(username, message);
            });
    }
}
