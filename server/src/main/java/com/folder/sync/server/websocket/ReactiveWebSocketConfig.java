package com.folder.sync.server.websocket;

import com.folder.sync.server.domain.model.FileSyncMessage;
import com.folder.sync.server.service.FileDataService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.lang.NonNull;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.simp.config.ChannelRegistration;
import org.springframework.messaging.simp.config.MessageBrokerRegistry;
import org.springframework.messaging.simp.stomp.StompCommand;
import org.springframework.messaging.simp.stomp.StompHeaderAccessor;
import org.springframework.messaging.support.ChannelInterceptor;
import org.springframework.messaging.support.MessageHeaderAccessor;
import org.springframework.web.reactive.HandlerMapping;
import org.springframework.web.reactive.handler.SimpleUrlHandlerMapping;
import org.springframework.web.reactive.socket.WebSocketHandler;
import org.springframework.web.reactive.socket.server.support.WebSocketHandlerAdapter;
import org.springframework.web.socket.config.annotation.EnableWebSocketMessageBroker;
import org.springframework.web.socket.config.annotation.StompEndpointRegistry;
import org.springframework.web.socket.config.annotation.WebSocketMessageBrokerConfigurer;

import java.util.HashMap;
import java.util.Map;

/**
 * Hybrid Reactive WebSocket Configuration
 * Supports both STOMP (for backward compatibility) and raw WebSocket (for reactive clients)
 */
@Configuration
@EnableWebSocketMessageBroker
@Slf4j
@RequiredArgsConstructor
public class ReactiveWebSocketConfig implements WebSocketMessageBrokerConfigurer {

    private final ReactiveWebSocketHandler reactiveWebSocketHandler;
    private final FileDataService fileDataService;

    /**
     * Configure STOMP endpoints for backward compatibility
     */
    @Override
    public void registerStompEndpoints(StompEndpointRegistry registry) {
        registry.addEndpoint("/app")
                .setAllowedOrigins("*")
                .withSockJS();

        registry.addEndpoint("/app")
                .setAllowedOrigins("*");
    }

    /**
     * Configure message broker for STOMP
     */
    @Override
    public void configureMessageBroker(MessageBrokerRegistry registry) {
        // Enable a simple message broker to carry the messages back to the client on destinations prefixed with /topic and /queue
        registry.enableSimpleBroker("/topic", "/queue");
        // Set the application destination prefix for all messages mapped with @MessageMapping
        registry.setApplicationDestinationPrefixes("/app");
        // Enable user-specific destinations
        registry.setUserDestinationPrefix("/user");
    }

    /**
     * Configure client inbound channel with reactive user directory creation and file list sending
     */
    @Override
    public void configureClientInboundChannel(ChannelRegistration registration) {
        registration.interceptors(new ChannelInterceptor() {
            @Override
            public Message<?> preSend(@NonNull Message<?> message, @NonNull MessageChannel channel) {
                StompHeaderAccessor accessor = MessageHeaderAccessor.getAccessor(message, StompHeaderAccessor.class);

                if (accessor != null && StompCommand.CONNECT.equals(accessor.getCommand())) {
                    // Extract username from the "user" header sent by the client
                    String username = accessor.getFirstNativeHeader("user");

                    if (username != null && !username.trim().isEmpty()) {
                        log.info("Setting user principal for session: {} with username: {}",
                                accessor.getSessionId(), username);

                        // Create and set the user principal
                        UserPrincipal userPrincipal = new UserPrincipal(username.trim());
                        accessor.setUser(userPrincipal);

                        // Create user directory and send file list reactively (non-blocking)
                        fileDataService.createUserDirectory(username.trim())
                            .then(fileDataService.getExistingFileNamesForUser(username.trim()).collectList())
                            .subscribe(
                                fileList -> {
                                    log.debug("User directory created for: {}", username);

                                    // Create file list message
                                    FileSyncMessage fileListMessage = FileSyncMessage.builder()
                                        .type("FILE_LIST")
                                        .fileList(fileList)
                                        .message("Connected successfully. Here are your existing files.")
                                        .build();

                                    // Send file list to the specific user via reactive WebSocket handler
                                    reactiveWebSocketHandler.sendToUser(username.trim(), fileListMessage)
                                        .subscribe(
                                            unused -> log.info("Sent file list with {} files to user: {}", fileList.size(), username),
                                            sendError -> log.error("Failed to send file list to user {}: {}", username, sendError.getMessage())
                                        );
                                },
                                error -> log.error("Failed to create user directory or get file list for: {}: {}", username, error.getMessage())
                            );

                    } else {
                        log.warn("No username found in connection headers for session: {}",
                                accessor.getSessionId());
                    }
                }

                return message;
            }
        });
    }

    /**
     * Configure WebSocket handler adapter for reactive WebSocket support
     */
    @Bean
    public WebSocketHandlerAdapter handlerAdapter() {
        return new WebSocketHandlerAdapter();
    }

    /**
     * Map raw WebSocket endpoints to reactive handlers (for future reactive clients)
     */
    @Bean
    public HandlerMapping webSocketMapping() {
        Map<String, WebSocketHandler> map = new HashMap<>();
        map.put("/reactive", reactiveWebSocketHandler);

        SimpleUrlHandlerMapping mapping = new SimpleUrlHandlerMapping();
        mapping.setUrlMap(map);
        mapping.setOrder(-1); // Before other mappings
        return mapping;
    }
}