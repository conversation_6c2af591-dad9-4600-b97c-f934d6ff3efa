package com.folder.sync.server.domain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

import java.time.LocalDateTime;

@Table("obj_data")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ObjData {

    @Id
    private Long id;

    private String candidateNo;
    private String schoolNo;
    private String subjectCode;
    private String serialNo;
    private String response;
    private String fileNo;
    private String userId;
    private String deviceId;
    private boolean discard;
    private int respCount;
    private int score;
    private LocalDateTime created = LocalDateTime.now();
    private LocalDateTime lastModified = LocalDateTime.now();
}
