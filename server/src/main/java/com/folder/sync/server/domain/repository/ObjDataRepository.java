package com.folder.sync.server.domain.repository;

import com.folder.sync.server.domain.model.FileData;
import com.folder.sync.server.domain.model.ObjData;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Repository
public interface ObjDataRepository extends R2dbcRepository<ObjData, Long> {

}
