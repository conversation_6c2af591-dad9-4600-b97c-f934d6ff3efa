package com.folder.sync.server.config;

import io.r2dbc.spi.ConnectionFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import org.springframework.data.r2dbc.config.AbstractR2dbcConfiguration;
import org.springframework.data.r2dbc.repository.config.EnableR2dbcRepositories;
import org.springframework.r2dbc.connection.init.ConnectionFactoryInitializer;
import org.springframework.r2dbc.connection.init.ResourceDatabasePopulator;

import org.springframework.r2dbc.connection.R2dbcTransactionManager;
import org.springframework.transaction.ReactiveTransactionManager;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider;
import org.springframework.core.type.filter.AnnotationTypeFilter;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.util.Set;

@Configuration
@EnableR2dbcRepositories(basePackages = "com.folder.sync.server.domain.repository")
public class R2dbcConfig extends AbstractR2dbcConfiguration {

    @Value("${spring.r2dbc.url}")
    private String r2dbcUrl;

    @Value("${spring.r2dbc.username}")
    private String username;

    @Value("${spring.r2dbc.password}")
    private String password;

    @Override
    @Bean
    public ConnectionFactory connectionFactory() {
        return io.r2dbc.spi.ConnectionFactories.get(
            io.r2dbc.spi.ConnectionFactoryOptions.builder()
                .option(io.r2dbc.spi.ConnectionFactoryOptions.DRIVER, "mysql")
                .option(io.r2dbc.spi.ConnectionFactoryOptions.HOST, "*********")
                .option(io.r2dbc.spi.ConnectionFactoryOptions.PORT, 3306)
                .option(io.r2dbc.spi.ConnectionFactoryOptions.DATABASE, "folder_sync")
                .option(io.r2dbc.spi.ConnectionFactoryOptions.USER, username)
                .option(io.r2dbc.spi.ConnectionFactoryOptions.PASSWORD, password)
                .option(io.r2dbc.spi.ConnectionFactoryOptions.SSL, false)
                .build()
        );
    }

    @Bean
    public ConnectionFactoryInitializer initializer(ConnectionFactory connectionFactory) {
        ConnectionFactoryInitializer initializer = new ConnectionFactoryInitializer();
        initializer.setConnectionFactory(connectionFactory);

        // Create a custom database populator that generates schema from entities
        ResourceDatabasePopulator populator = new ResourceDatabasePopulator();

        // Generate DDL for entities
        String ddlScript = generateDDLFromEntities();

        // Create a temporary resource with the generated DDL
        populator.addScript(new org.springframework.core.io.ByteArrayResource(ddlScript.getBytes()));
        populator.setContinueOnError(true); // Continue if tables already exist
        initializer.setDatabasePopulator(populator);

        return initializer;
    }

    /**
     * Generate DDL statements from R2DBC entity classes dynamically
     */
    private String generateDDLFromEntities() {
        StringBuilder ddl = new StringBuilder();

        try {
            // Scan for entity classes in the domain model package
            ClassPathScanningCandidateComponentProvider scanner = new ClassPathScanningCandidateComponentProvider(false);
            scanner.addIncludeFilter(new AnnotationTypeFilter(Table.class));

            Set<BeanDefinition> entityClasses = scanner.findCandidateComponents("com.folder.sync.server.domain.model");

            for (BeanDefinition beanDefinition : entityClasses) {
                Class<?> entityClass = Class.forName(beanDefinition.getBeanClassName());
                String tableDDL = generateTableDDL(entityClass);
                ddl.append(tableDDL);
            }

        } catch (Exception e) {
            // Fallback to basic tables if reflection fails
            ddl.append("CREATE TABLE IF NOT EXISTS file_data (")
               .append("id BIGINT AUTO_INCREMENT PRIMARY KEY, ")
               .append("original_file_path VARCHAR(255) NOT NULL, ")
               .append("content LONGTEXT NOT NULL, ")
               .append("file_size BIGINT NOT NULL, ")
               .append("last_modified BIGINT NOT NULL, ")
               .append("received_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP, ")
               .append("username VARCHAR(255) NOT NULL DEFAULT 'unknown'")
               .append(");\n");

            ddl.append("CREATE TABLE IF NOT EXISTS user_sessions (")
               .append("id BIGINT AUTO_INCREMENT PRIMARY KEY, ")
               .append("session_id VARCHAR(255) NOT NULL UNIQUE, ")
               .append("username VARCHAR(255) NOT NULL, ")
               .append("connected_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP, ")
               .append("last_activity TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, ")
               .append("is_active BOOLEAN NOT NULL DEFAULT TRUE")
               .append(");\n");
        }

        return ddl.toString();
    }

    /**
     * Generate CREATE TABLE DDL for a specific entity class
     */
    private String generateTableDDL(Class<?> entityClass) {
        Table tableAnnotation = entityClass.getAnnotation(Table.class);
        if (tableAnnotation == null) {
            return "";
        }

        String tableName = tableAnnotation.value();
        StringBuilder ddl = new StringBuilder();
        ddl.append("CREATE TABLE IF NOT EXISTS ").append(tableName).append(" (");

        Field[] fields = entityClass.getDeclaredFields();
        boolean firstField = true;

        for (Field field : fields) {
            if (!firstField) {
                ddl.append(", ");
            }

            String columnName = getColumnName(field);
            String columnType = getColumnType(field);

            ddl.append(columnName).append(" ").append(columnType);

            // Add PRIMARY KEY for @Id fields
            if (field.isAnnotationPresent(Id.class)) {
                ddl.append(" AUTO_INCREMENT PRIMARY KEY");
            }

            firstField = false;
        }

        ddl.append(");\n");
        return ddl.toString();
    }

    /**
     * Get column name from field (uses @Column annotation or field name)
     */
    private String getColumnName(Field field) {
        Column columnAnnotation = field.getAnnotation(Column.class);
        if (columnAnnotation != null && !columnAnnotation.value().isEmpty()) {
            return columnAnnotation.value();
        }
        // Convert camelCase to snake_case
        return field.getName().replaceAll("([a-z])([A-Z])", "$1_$2").toLowerCase();
    }

    /**
     * Get MySQL column type based on Java field type
     */
    private String getColumnType(Field field) {
        Class<?> fieldType = field.getType();

        if (fieldType == Long.class || fieldType == long.class) {
            return "BIGINT";
        } else if (fieldType == Integer.class || fieldType == int.class) {
            return "INT";
        } else if (fieldType == Boolean.class || fieldType == boolean.class) {
            return "BOOLEAN";
        } else if (fieldType == LocalDateTime.class) {
            return "TIMESTAMP DEFAULT CURRENT_TIMESTAMP";
        } else if (fieldType == String.class) {
            // Use LONGTEXT for content fields, VARCHAR for others
            if (field.getName().toLowerCase().contains("content")) {
                return "LONGTEXT";
            }
            return "VARCHAR(255)";
        }

        return "VARCHAR(255)"; // Default fallback
    }

    @Bean
    public ReactiveTransactionManager transactionManager(ConnectionFactory connectionFactory) {
        return new R2dbcTransactionManager(connectionFactory);
    }
}
