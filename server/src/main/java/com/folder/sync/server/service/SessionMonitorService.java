package com.folder.sync.server.service;

import com.folder.sync.server.domain.model.UserSession;
import com.folder.sync.server.domain.model.SessionMonitorMessage;
import com.folder.sync.server.domain.model.FileSyncMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;

@Service
@Slf4j
public class SessionMonitorService {

    private final UserSessionService userSessionService;
    private final FileDataService fileDataService;

    // Lazy injection to avoid circular dependency
    @Lazy
    private com.folder.sync.server.websocket.ReactiveSessionManager reactiveSessionManager;

    @Autowired
    public SessionMonitorService(UserSessionService userSessionService,
                                       FileDataService fileDataService) {
        this.userSessionService = userSessionService;
        this.fileDataService = fileDataService;
    }

    @Autowired
    @Lazy
    public void setReactiveSessionManager(com.folder.sync.server.websocket.ReactiveSessionManager reactiveSessionManager) {
        this.reactiveSessionManager = reactiveSessionManager;
    }
    
    /**
     * Broadcasts session statistics to all monitoring clients reactively
     */
    public Mono<Void> broadcastSessionStats() {
        return generateSessionStats()
            .map(SessionMonitorMessage::createStatsMessage)
            .flatMap(message -> {
                // Convert SessionMonitorMessage to FileSyncMessage for broadcasting
                FileSyncMessage broadcastMessage = FileSyncMessage.builder()
                    .type("SESSION_STATS")
                    .message("Session statistics update")
                    .build();

                if (reactiveSessionManager != null) {
                    return reactiveSessionManager.broadcast(broadcastMessage)
                        .doOnSuccess(unused -> log.debug("Broadcasted session stats: {} active sessions",
                            message.getStats().getTotalActiveSessions()));
                } else {
                    log.warn("ReactiveSessionManager not available for broadcasting");
                    return Mono.empty();
                }
            })
            .doOnError(error -> log.error("Error broadcasting session stats: {}", error.getMessage()))
            .onErrorResume(error -> Mono.empty());
    }
    
    /**
     * Broadcasts complete session list with stats to monitoring clients reactively
     */
    public Mono<Void> broadcastSessionList() {
        return userSessionService.getAllActiveSessions()
            .flatMap(this::convertToSessionInfo)
            .collectList()
            .zipWith(generateSessionStats())
            .map(tuple -> SessionMonitorMessage.createSessionListMessage(tuple.getT1(), tuple.getT2()))
            .flatMap(message -> {
                // Convert SessionMonitorMessage to FileSyncMessage for broadcasting
                FileSyncMessage broadcastMessage = FileSyncMessage.builder()
                    .type("SESSION_LIST")
                    .message("Session list update")
                    .build();

                if (reactiveSessionManager != null) {
                    return reactiveSessionManager.broadcast(broadcastMessage)
                        .doOnSuccess(unused -> log.debug("Broadcasted session list with {} active sessions",
                            message.getActiveSessions().size()));
                } else {
                    log.warn("ReactiveSessionManager not available for broadcasting");
                    return Mono.empty();
                }
            })
            .doOnError(error -> log.error("Error broadcasting session list: {}", error.getMessage(), error))
            .onErrorResume(error -> Mono.empty());
    }
    
    /**
     * Broadcasts a session event to monitoring clients reactively
     */
    public Mono<Void> broadcastSessionEvent(String eventType, String sessionId, String username, String details) {
        return Mono.fromCallable(() -> {
            SessionMonitorMessage.SessionEvent event = new SessionMonitorMessage.SessionEvent(
                eventType, sessionId, username, LocalDateTime.now(), details
            );
            return SessionMonitorMessage.createEventMessage(event);
        })
        .flatMap(message -> {
            // Convert SessionMonitorMessage to FileSyncMessage for broadcasting
            FileSyncMessage broadcastMessage = FileSyncMessage.builder()
                .type("SESSION_EVENT")
                .message(String.format("Session event: %s for user %s", eventType, username))
                .build();

            if (reactiveSessionManager != null) {
                return reactiveSessionManager.broadcast(broadcastMessage)
                    .doOnSuccess(unused -> log.debug("Broadcasted session event: {} for user {} (session: {})",
                        eventType, username, sessionId));
            } else {
                log.warn("ReactiveSessionManager not available for broadcasting");
                return Mono.empty();
            }
        })
        .doOnError(error -> log.error("Error broadcasting session event: {}", error.getMessage(), error))
        .onErrorResume(error -> Mono.empty())
        .subscribeOn(Schedulers.boundedElastic());
    }
    
    /**
     * Generates current session statistics reactively
     */
    private Mono<SessionMonitorMessage.SessionStats> generateSessionStats() {
        return userSessionService.getAllActiveSessions()
            .collectList()
            .zipWith(fileDataService.getFileCount())
            .map(tuple -> {
                List<UserSession> activeSessions = tuple.getT1();
                long totalFiles = tuple.getT2();

                long totalActiveSessions = activeSessions.size();
                long totalUsers = activeSessions.stream()
                        .map(UserSession::getUsername)
                        .distinct()
                        .count();
                
                double averageSessionDuration = activeSessions.stream()
                        .mapToLong(session -> Duration.between(session.getConnectedAt(), LocalDateTime.now()).toMinutes())
                        .average()
                        .orElse(0.0);
                
                return new SessionMonitorMessage.SessionStats(
                    totalActiveSessions,
                    totalUsers,
                    totalFiles,
                    averageSessionDuration,
                    LocalDateTime.now()
                );
            });
    }
    
    /**
     * Converts UserSession to SessionInfo for monitoring reactively
     */
    private Mono<SessionMonitorMessage.SessionInfo> convertToSessionInfo(UserSession session) {
        return fileDataService.getFileCountForUser(session.getUsername())
            .map(filesUploaded -> {
                long sessionDuration = Duration.between(session.getConnectedAt(), LocalDateTime.now()).toMinutes();
                
                return new SessionMonitorMessage.SessionInfo(
                    session.getSessionId(),
                    session.getUsername(),
                    session.getConnectedAt(),
                    session.getLastActivity(),
                    session.getIsActive(),
                    filesUploaded,
                    "N/A", // IP address not tracked in current implementation
                    sessionDuration
                );
            });
    }
    
    /**
     * Gets current session statistics without broadcasting
     */
    public Mono<SessionMonitorMessage.SessionStats> getCurrentStats() {
        return generateSessionStats();
    }
    
    /**
     * Gets current sessions without broadcasting
     */
    public Flux<SessionMonitorMessage.SessionInfo> getCurrentSessions() {
        return userSessionService.getAllActiveSessions()
            .flatMap(this::convertToSessionInfo);
    }
    
    /**
     * Creates a reactive stream for periodic session stats broadcasting
     */
    public Flux<Void> createPeriodicStatsStream(Duration interval) {
        return Flux.interval(interval)
            .flatMap(tick -> broadcastSessionStats())
            .doOnError(error -> log.error("Error in periodic stats stream: {}", error.getMessage(), error))
            .onErrorContinue((error, item) -> log.warn("Continuing after error in stats stream: {}", error.getMessage()));
    }
    
    /**
     * Creates a reactive stream for periodic session list broadcasting
     */
    public Flux<Void> createPeriodicSessionListStream(Duration interval) {
        return Flux.interval(interval)
            .flatMap(tick -> broadcastSessionList())
            .doOnError(error -> log.error("Error in periodic session list stream: {}", error.getMessage(), error))
            .onErrorContinue((error, item) -> log.warn("Continuing after error in session list stream: {}", error.getMessage()));
    }
}
