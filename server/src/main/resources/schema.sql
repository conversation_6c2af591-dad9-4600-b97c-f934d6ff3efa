-- Create tables
CREATE TABLE IF NOT EXISTS file_data (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    original_file_path VARCHAR(255) NOT NULL,
    content LONGTEXT NOT NULL,
    file_size BIGINT NOT NULL,
    last_modified BIGINT NOT NULL,
    received_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    username VARCHAR(255) NOT NULL DEFAULT 'unknown'
);

CREATE TABLE IF NOT EXISTS user_sessions (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    session_id VARCHAR(255) NOT NULL UNIQUE,
    username VARCHAR(255) NOT NULL,
    connected_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_activity TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    is_active BOOLEAN NOT NULL DEFAULT TRUE
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_file_received_at ON file_data(received_at);
CREATE INDEX IF NOT EXISTS idx_file_path ON file_data(original_file_path);
CREATE INDEX IF NOT EXISTS idx_file_username ON file_data(username);
CREATE INDEX IF NOT EXISTS idx_file_username_received_at ON file_data(username, received_at);
CREATE INDEX IF NOT EXISTS idx_session_id ON user_sessions(session_id);
CREATE INDEX IF NOT EXISTS idx_username ON user_sessions(username);
CREATE INDEX IF NOT EXISTS idx_session_active ON user_sessions(is_active);
