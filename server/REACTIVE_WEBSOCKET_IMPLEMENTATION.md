# Reactive WebSocket Implementation

## Overview

The folder sync server has been upgraded from traditional STOMP-based WebSocket configuration to a fully reactive WebSocket implementation using Spring WebFlux. This provides better scalability, non-blocking I/O, and improved performance.

## Architecture Changes

### Before (Traditional STOMP)
- `WebSocketConfig` with `@EnableWebSocketMessageBroker`
- `FileSyncWebSocketController` with `@MessageMapping`
- Blocking operations with `.block()` calls
- Thread-per-connection model

### After (Reactive WebFlux)
- `ReactiveWebSocketConfig` with Spring WebFlux WebSocket support
- `ReactiveWebSocketHandler` implementing `WebSocketHandler`
- Fully non-blocking reactive streams
- Event-loop based concurrency model

## Key Components

### 1. ReactiveWebSocketConfig
- **Location**: `server/src/main/java/com/folder/sync/server/websocket/ReactiveWebSocketConfig.java`
- **Purpose**: Configures reactive WebSocket endpoints and handlers
- **Features**:
  - Maps `/app` endpoint to `ReactiveWebSocketHandler`
  - Configures `WebSocketHandlerAdapter` for reactive support
  - Replaces traditional STOMP configuration

### 2. ReactiveWebSocketHandler
- **Location**: `server/src/main/java/com/folder/sync/server/websocket/ReactiveWebSocketHandler.java`
- **Purpose**: Handles all WebSocket connections and message processing reactively
- **Features**:
  - Implements `WebSocketHandler` interface
  - Manages session lifecycle reactively
  - Processes messages using reactive streams
  - Maintains session-to-user mapping
  - Supports user-specific messaging

### 3. ReactiveSessionManager
- **Location**: `server/src/main/java/com/folder/sync/server/websocket/ReactiveSessionManager.java`
- **Purpose**: Manages WebSocket sessions reactively
- **Features**:
  - Session registration/unregistration
  - User-to-session mapping
  - Broadcasting capabilities
  - Session activity tracking

### 4. ReactiveFileSyncController
- **Location**: `server/src/main/java/com/folder/sync/server/controller/ReactiveFileSyncController.java`
- **Purpose**: Provides REST endpoints that integrate with reactive WebSocket system
- **Features**:
  - Server-Sent Events (SSE) for real-time updates
  - REST API for session management
  - File operations integration
  - Broadcasting via WebSocket

## Message Flow

### Connection Flow
1. Client connects to `/app` WebSocket endpoint
2. `ReactiveWebSocketHandler.handle()` is called
3. Session is registered in `activeSessions` map
4. Client sends `CONNECT` message with username
5. User directory is created reactively
6. File list is sent to user
7. Session is tracked in `sessionUsernames` map

### Message Processing Flow
1. Client sends JSON message via WebSocket
2. `handleIncomingMessage()` deserializes message
3. `processMessage()` routes based on message type:
   - `CONNECT`: Authentication and setup
   - `FILE_UPLOAD`: File processing
   - `FILE_LIST_REQUEST`: Send file list
   - `PING`: Health check response
4. Response is sent back via session sink

### File Upload Flow
1. Client sends `FILE_UPLOAD` message
2. `handleFileUpload()` processes the file
3. `FileDataService.processFileData()` saves file reactively
4. Success response sent to client
5. File list can be refreshed for user

## Benefits of Reactive Implementation

### Performance
- **Non-blocking I/O**: No thread blocking on I/O operations
- **Better resource utilization**: Event-loop based concurrency
- **Lower memory footprint**: Fewer threads needed
- **Higher throughput**: Can handle more concurrent connections

### Scalability
- **Reactive streams**: Backpressure handling
- **Asynchronous processing**: All operations are non-blocking
- **Resource efficiency**: Better CPU and memory usage
- **Connection pooling**: More efficient connection management

### Maintainability
- **Consistent reactive patterns**: All services use Mono/Flux
- **Better error handling**: Reactive error propagation
- **Cleaner code**: Functional programming style
- **Testability**: Easier to test with StepVerifier

## Configuration

### Application Properties
```yaml
server:
  port: 8090

spring:
  webflux:
    base-path: /api
```

### WebSocket Endpoint
- **URL**: `ws://localhost:8090/app`
- **Protocol**: Raw WebSocket (not STOMP)
- **Message Format**: JSON

## Message Types

### Client to Server
- `CONNECT`: Initial connection with username
- `FILE_UPLOAD`: Upload file data
- `FILE_LIST_REQUEST`: Request current file list
- `PING`: Health check

### Server to Client
- `FILE_LIST`: List of available files
- `FILE_UPLOAD_SUCCESS`: File upload confirmation
- `PONG`: Ping response
- `ERROR`: Error messages

## Migration Notes

### Disabled Components
The following traditional components have been disabled:
- `FileSyncWebSocketController` (commented out `@Controller`)
- `SessionMonitorController` (commented out `@Controller`)

### Backward Compatibility
- Message formats remain the same
- Client connection URL unchanged (`/app`)
- All existing functionality preserved

### Testing
- Unit tests for reactive components
- Integration tests with `WebTestClient`
- Reactive test utilities with `StepVerifier`

## Usage Examples

### Sending Message to Specific User
```java
@Autowired
private ReactiveSessionManager sessionManager;

public Mono<Void> notifyUser(String username, String message) {
    FileSyncMessage notification = FileSyncMessage.builder()
        .type("NOTIFICATION")
        .message(message)
        .build();
    return sessionManager.sendToUser(username, notification);
}
```

### Broadcasting to All Users
```java
public Mono<Void> broadcastUpdate(String message) {
    FileSyncMessage broadcast = FileSyncMessage.builder()
        .type("BROADCAST")
        .message(message)
        .build();
    return sessionManager.broadcast(broadcast);
}
```

### Server-Sent Events
```java
@GetMapping(value = "/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
public Flux<SessionInfo> getSessionStream() {
    return sessionManager.getActiveSessions()
        .delayElements(Duration.ofSeconds(1));
}
```

## Monitoring and Debugging

### REST Endpoints
- `GET /api/reactive/sessions/count`: Active session count
- `GET /api/reactive/sessions`: List active sessions
- `GET /api/reactive/users/{username}/online`: Check if user is online
- `GET /api/reactive/health`: Health check

### Logging
- Session lifecycle events
- Message processing
- Error handling
- Performance metrics

## Future Enhancements

1. **WebSocket Security**: Add authentication/authorization
2. **Rate Limiting**: Implement backpressure for message handling
3. **Clustering**: Support for multiple server instances
4. **Metrics**: Add reactive metrics collection
5. **Circuit Breaker**: Add resilience patterns
